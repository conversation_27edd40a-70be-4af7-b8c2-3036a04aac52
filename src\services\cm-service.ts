import { invoke } from "@tauri-apps/api/core";
import {
  SnoozedEmail,      // = EmailLog model
  SnoozedEmailWithDetails,
} from "../models/cm-model";
import { EmailCategory } from "../../types";

/* ------------------------------------------------------------------ */
/*  Categorization                                                    */
/* ------------------------------------------------------------------ */

export async function listCategorizedEmails(
  filter?: string
): Promise<EmailCategory[]> {
  return await invoke<EmailCategory[]>("list_categorized_emails", { filter });
}

export async function categorizeEmail(
  emailId: string,
  userId: number
): Promise<EmailCategory> {
  return await invoke<EmailCategory>("categorize_email", { emailId, userId });
}

export async function listEmailCategories(): Promise<EmailCategory[]> {
  return await invoke<EmailCategory[]>("get_email_categories", {});
}

/* ------------------------------------------------------------------ */
/*  Snoozing / Resurfacing                                            */
/* ------------------------------------------------------------------ */

export async function snoozeEmail(
  emailId: string,
  snoozedUntil: string
): Promise<SnoozedEmail> {
  return await invoke<SnoozedEmail>("snooze_email", { emailId, snoozedUntil });
}

/** All logs, optionally include resurfaced ones from the history tab */
export async function getSnoozedEmails(): Promise<SnoozedEmail[]> {
  return await invoke<SnoozedEmail[]>("list_snoozed_emails", {});
}

/** Only future-due snoozes (resurfaced = false, snoozed_until > now) */
export async function getActiveSnoozedEmails(): Promise<SnoozedEmail[]> {
  return await invoke<SnoozedEmail[]>("get_active_snoozed_emails", {});
}

/** Get snoozed emails with their full email details */
export async function getSnoozedEmailsWithDetails(): Promise<SnoozedEmailWithDetails[]> {
  console.log("🔄 Calling get_snoozed_emails_with_details backend command...");
  try {
    const result = await invoke<SnoozedEmailWithDetails[]>("get_snoozed_emails_with_details", {});
    console.log("✅ Backend returned:", result);
    return result;
  } catch (error) {
    console.error("❌ Backend call failed:", error);
    throw error;
  }
}

/** Manually cancel a snooze (deletes the log entry) */
export async function cancelSnooze(emailLogId: string): Promise<void> {
  await invoke<void>("delete_email_log", { id: emailLogId });
}

/** Fetch a single snooze log row (e.g., for detail panel) */
export async function getEmailLogById(
  emailLogId: string
): Promise<SnoozedEmail | null> {
  return await invoke<SnoozedEmail | null>("get_email_log_by_id", {
    id: emailLogId,
  });
}

/** Trigger resurfacing of any emails whose snooze has expired */
export async function resurfaceDueEmails(): Promise<number> {
  // returns number of emails marked as resurfaced
  return await invoke<number>("resurface_due_emails", {});
}

/* ------------------------------------------------------------------ */
/*  Aggregated export for easy import                                 */
/* ------------------------------------------------------------------ */

export const CategorizationManagerService = {
  /* categorization */
  listCategorizedEmails,
  categorizeEmail,
  listEmailCategories,

  /* snoozing / resurfacing */
  snoozeEmail,
  getSnoozedEmails,
  getActiveSnoozedEmails,
  getSnoozedEmailsWithDetails,
  cancelSnooze,
  getEmailLogById,
  resurfaceDueEmails,
};
