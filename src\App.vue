<template>
  <TitleBar v-if="!isSettingsPage" />
  <div>
    <SubscriptionGate v-if="!isPaid" />

    <router-view v-else />
    <!-- Show rest of the app if paid -->
    <notification />
    <div></div>
  </div>
  <Toaster richColors :expand="true" />
</template>
<script setup lang="ts">
import { ref, onMounted } from "vue";
import Notification from "./components/Notification.vue";
import { useCurrentAssistantStore } from "./stores/currentAssistant";
import { useCurrentSessionStore, useCurrentEmailCategoryStore } from "./stores/currentSession";
import TitleBar from "./components/TitleBar.vue";
import SubscriptionGate from "./pages/SubscriptionGate.vue";
import { computed } from "vue";
import { useRoute } from "vue-router";
import { useWeatherStore } from "./stores/weatherStore";
import { toast, Toaster } from "vue-sonner";

import { checkPaymentStatus } from "./services/payment"; // Adjust the import path as necessary
import { useBookingStore } from "./stores/bookingsStore";
import { useMeetingsStore } from "./stores/meetingsStore";
import auth from "./commands/auth";
import { useCurrentUserStore } from "./stores/currentUser";
import { listen } from "@tauri-apps/api/event";
// import { useCurrentUserStore } from './stores/currentUser';
const route = useRoute();
const isSettingsPage = computed(() => route.path === "/settings");

const currentAsssistantStore = useCurrentAssistantStore();
const currentSessionStore = useCurrentSessionStore();
const currentEmailCategoryStore = useCurrentEmailCategoryStore();
const currentUserStore = useCurrentUserStore();
const isPaid = ref(true);
const weatherStore = useWeatherStore();
const bookingsStore = useBookingStore();
const meetingsStore = useMeetingsStore();

onMounted(async () => {
  const info = await auth.getLocalUserInfo();
  console.log("🚀 ~ onMounted ~ info:", info);

  await currentAsssistantStore.init();
  await currentSessionStore.init();
  await currentEmailCategoryStore.init();
  await currentUserStore.init();
  await weatherStore.init();
  await bookingsStore.fetchTodayBookings();
  await meetingsStore.refresh();

  console.log("🚀Current user Store", currentUserStore.calInfo);
  console.log("🚀Current user Store", currentUserStore.currentUser);

  navigator.geolocation.getCurrentPosition(
    (pos) => {
      localStorage.setItem("gps", JSON.stringify(pos.coords));
      //  console.log("POS", pos);
    },
    (err) => {
      console.error("Localization ERROR", err);
    }
  );
  const email = localStorage.getItem("email");
  if (email) {
    const result = await checkPaymentStatus(email);
    isPaid.value = result;
  }

  await listen("user:update", async () => {
    const result = await currentUserStore.init();
    if (result) toast.info("User info updated");
  });
});
</script>
<style scoped></style>
