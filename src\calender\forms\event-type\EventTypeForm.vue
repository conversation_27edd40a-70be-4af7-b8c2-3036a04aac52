<template>
  <div class="size-full flex flex-col" :key="data.title">
    <!-- Header Section -->
    <div
      class="w-full flex justify-between items-center bg-primary p-4 rounded-lg shadow-sm border border-primary-300 mb-4"
    >
      <!-- Step Navigation -->
      <div class="flex items-center gap-3">
        <div
          class="flex items-center gap-2 cursor-pointer transition-all duration-200"
          v-for="(step, index) of steps"
          :key="step"
          @click="currentStep = step"
        >
          <div
            :class="{
              'bg-secondary-200 text-secondary-700': currentStep != step,
              'bg-highlight-500 text-primary': currentStep === step,
            }"
            class="w-8 h-8 font-semibold flex justify-center items-center rounded-full text-sm transition-all duration-200"
          >
            {{ index + 1 }}
          </div>
          <span
            :class="{
              'text-secondary-600': currentStep != step,
              'text-base-500 font-medium': currentStep === step,
            }"
            class="text-sm transition-all duration-200"
          >
            {{ step }}
          </span>
          <ChevronRightIcon v-if="index != steps.length - 1" class="w-4 h-4 text-secondary-500 mx-2" />
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-2">
        <button
          @click="data = defaultEventType"
          class="p-2 bg-primary-200 text-secondary-700 hover:bg-primary-300 rounded-lg transition-colors duration-200"
          title="Reset form"
        >
          <ArrowPathIcon class="w-4 h-4" />
        </button>
        <button
          @click="() => (data.id == 0 ? create() : save())"
          :disabled="isLoading"
          class="flex items-center gap-2 px-4 py-2 bg-highlight-500 text-primary rounded-lg hover:bg-highlight-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium min-w-[100px] justify-center"
        >
          <l-ring v-if="isLoading" size="16" stroke="2" bg-opacity="0" speed="2" color="#F9F4ED"></l-ring>
          <span v-else>{{ data.id == 0 ? "Create Event" : "Save Changes" }}</span>
        </button>
      </div>
    </div>

    <!-- Content Section -->
    <div class="flex-1 overflow-y-auto custom-scrollbar">
      <Setup v-show="currentStep === 'Setup'" :setup="data" @change="updateData" />
      <AvailablityStep
        v-show="currentStep === 'Availability'"
        :selected-id="data.scheduleId === 0 ? undefined : data.scheduleId"
        @change="(value:number)=>updateData({scheduleId:value})"
      />
      <LimitsStep v-show="currentStep === 'Limits'" :limits="data" @change="updateData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowPathIcon, ChevronRightIcon } from "@heroicons/vue/24/outline";
import { onMounted, ref, watch } from "vue";
import Setup from "./steps/Setup.vue";
import AvailablityStep from "./steps/AvailablityStep.vue";
import LimitsStep from "./steps/LimitsStep.vue";
import { defaultEventType, EventType } from "../../../models/event-type-model";
import { calEventTypeService } from "../../../services/event-type-cal-service";
import { ring } from "ldrs";
import { useCurrentUserStore } from "../../../stores/currentUser";
import { toast } from "vue-sonner";

ring.register();

type Steps = "Setup" | "Availability" | "Limits";
const steps: Steps[] = ["Setup", "Availability", "Limits"];
const currentStep = ref<Steps>("Setup");
const isLoading = ref(false);

const props = defineProps({
  data: {
    type: Object,
  },
});

const data = ref<EventType>((props.data as EventType) ?? defaultEventType);

function updateData(value: Partial<EventType>) {
  data.value = {
    ...data.value,
    ...value,
  };

  //  console.log("updated Data ===> ", data.value);
}

watch(
  data,
  (value) => {
    //  console.log("#### ACTUAL DATA=>", value);
  },

  { deep: true }
);

onMounted(() => {
  //  console.log("#### ACTUAL DATA   =>", data.value);
});
const session = useCurrentUserStore();

async function create() {
  //  console.log("############### CREATE EVENT ##############\n");
  //  console.table([data.value]);
  try {
    if (data.value && data.value.title && data.value.slug && session.calInfo?.id) {
      isLoading.value = true;
      data.value.userId = session.calInfo?.id;
      delete data.value.recurringEvent;
      // await calEventTypeService.createEventType(data.value);
      toast.promise(() => calEventTypeService.createEventType(data.value), {
        loading: "Creating...",
        success: () => {
          return `Event has been created!`;
        },
        error: () => "Couldn't create the event!",
      });
      isLoading.value = false;
    } else toast.error("Invalid event data!");
  } catch (error: any) {
    console.error(error);
    toast.error(error.message);
  }
}

async function save() {
  try {
    //  console.log("############### SAVE EVENT ##############\n");
    //  console.table([data.value]);
    //  console.log(`SAVE EVENT ${data.value.title} with is ${data.value.id}`);
    if (
      data.value &&
      data.value.id &&
      data.value.id != 0 &&
      session.calInfo?.id &&
      data.value.title &&
      data.value.slug
    ) {
      isLoading.value = true;
      data.value.userId = session.calInfo?.id;
      delete data.value.seatsPerTimeSlot;
      delete data.value.recurringEvent;
      // await calEventTypeService.updateEventType(data.value.id, data.value);
      toast.promise(() => calEventTypeService.updateEventType(data.value.id!, data.value), {
        loading: "Saving...",
        success: () => {
          return `Event has been saved!`;
        },
        error: () => "Couldn't save the event!",
      });
      isLoading.value = false;
    } else toast.error("Invalid event data!");
  } catch (error: any) {
    console.error("EventTypeForm:Save:Error", error);
    toast.error(error.message);
  }
}
</script>

<style scoped></style>
