<template>
  <div class="border-t border-primary-200 bg-primary-50 px-6 py-4">
    <div class="flex items-center justify-between">
      <!-- Primary Actions -->
      <div class="flex gap-3">
        <button
          @click="$emit('reply')"
          class="flex items-center gap-2 bg-secondary-400 hover:bg-secondary-500 
                 text-primary-100 px-4 py-2 rounded-lg transition-colors duration-200 
                 font-medium shadow-sm"
        >
          <i class="pi pi-reply text-sm"></i>
          <span>Reply</span>
        </button>
        
        <button
          @click="$emit('replyAll')"
          class="flex items-center gap-2 bg-primary-200 hover:bg-primary-300 
                 text-secondary-700 px-4 py-2 rounded-lg transition-colors duration-200 
                 font-medium border border-primary-300"
        >
          <i class="pi pi-reply-all text-sm"></i>
          <span>Reply All</span>
        </button>
        
        <button
          @click="$emit('forward')"
          class="flex items-center gap-2 bg-primary-200 hover:bg-primary-300 
                 text-secondary-700 px-4 py-2 rounded-lg transition-colors duration-200 
                 font-medium border border-primary-300"
        >
          <i class="pi pi-share-alt text-sm"></i>
          <span>Forward</span>
        </button>
      </div>

      <!-- Secondary Actions -->
      <div class="flex gap-2">
        <button
          @click="triggerFileUpload"
          class="flex items-center gap-2 bg-primary-200 hover:bg-primary-300 
                 text-secondary-700 px-3 py-2 rounded-lg transition-colors duration-200 
                 border border-primary-300"
          title="Upload Attachment"
        >
          <i class="pi pi-paperclip text-sm"></i>
          <span class="hidden sm:inline">Attach</span>
        </button>
        
        <button
          v-if="showAutoReply"
          @click="$emit('autoReply')"
          class="flex items-center gap-2 bg-highlight-400 hover:bg-highlight-500 
                 text-primary-100 px-3 py-2 rounded-lg transition-colors duration-200 
                 border border-highlight-300"
          title="Generate Auto Reply"
        >
          <i class="pi pi-microchip-ai text-sm"></i>
          <span class="hidden sm:inline">AI Reply</span>
        </button>
        
        <div class="relative">
          <button
            @click="toggleMoreActions"
            class="flex items-center gap-1 bg-primary-200 hover:bg-primary-300 
                   text-secondary-700 px-3 py-2 rounded-lg transition-colors duration-200 
                   border border-primary-300"
            title="More Actions"
          >
            <i class="pi pi-ellipsis-h text-sm"></i>
          </button>
          
          <!-- More Actions Dropdown -->
          <div
            v-if="showMoreActions"
            class="absolute bottom-full right-0 mb-2 bg-primary-100 border border-primary-300 
                   rounded-lg shadow-lg py-2 min-w-40 z-10"
          >
            <button
              @click="handleAction('import')"
              class="w-full text-left px-4 py-2 text-sm text-secondary-700 
                     hover:bg-primary-200 transition-colors duration-200 flex items-center gap-2"
            >
              <i class="pi pi-file-import text-xs"></i>
              Import
            </button>
            <button
              @click="handleAction('export')"
              class="w-full text-left px-4 py-2 text-sm text-secondary-700 
                     hover:bg-primary-200 transition-colors duration-200 flex items-center gap-2"
            >
              <i class="pi pi-file-export text-xs"></i>
              Export
            </button>
            <button
              @click="handleAction('print')"
              class="w-full text-left px-4 py-2 text-sm text-secondary-700 
                     hover:bg-primary-200 transition-colors duration-200 flex items-center gap-2"
            >
              <i class="pi pi-print text-xs"></i>
              Print
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Hidden File Input -->
    <input 
      ref="fileInput" 
      type="file" 
      @change="handleFileUpload" 
      class="hidden" 
      multiple 
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  showAutoReply?: boolean
}

withDefaults(defineProps<Props>(), {
  showAutoReply: true
})

defineEmits<{
  reply: []
  replyAll: []
  forward: []
  autoReply: []
  uploadAttachment: [files: FileList]
  import: []
  export: []
  print: []
}>()

const fileInput = ref<HTMLInputElement>()
const showMoreActions = ref(false)

const toggleMoreActions = () => {
  showMoreActions.value = !showMoreActions.value
}

const triggerFileUpload = () => {
  fileInput.value?.click()
}

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    // Emit the files to parent component
    console.log('Files selected:', target.files)
    // You can emit this to parent: emit('uploadAttachment', target.files)
  }
}

const handleAction = (action: string) => {
  showMoreActions.value = false
  
  switch (action) {
    case 'import':
      console.log('Import action')
      break
    case 'export':
      console.log('Export action')
      break
    case 'print':
      window.print()
      break
  }
}

// Close dropdown when clicking outside
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showMoreActions.value = false
  }
})
</script>
