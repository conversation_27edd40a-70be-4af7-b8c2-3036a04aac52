<template>
  <div class="flex h-full flex-col overflow-hidden pb-2 bg-primary">
    <div class="w-full flex justify-between items-center px-4 py-1">
      <div class="flex items-center gap-2">
        <h2 class="text-lg font-semibold text-base-800">Snoozed Emails</h2>
        <span class="text-x text-base-600">({{ snoozedEmails.length }})</span>
      </div>
      <div class="flex gap-2">
        <button
          @click="refreshSnoozedEmails"
          class="size-6 rounded pt-0.5 hover:bg-primary-600/20 transition-colors"
          title="Refresh Snoozed Emails"
          :disabled="isLoading"
        >
          <i class="pi pi-refresh" :class="{ 'pi-spin': isLoading }"></i>
        </button>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="flex items-center gap-2 text-base-600">
        <i class="pi pi-spin pi-spinner"></i>
        <span>Loading snoozed emails...</span>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else-if="snoozedEmails.length === 0" class="flex flex-col justify-center items-center py-8 text-center">
      <i class="pi pi-clock text-4xl text-base-400 mb-4"></i>
      <h3 class="text-lg font-medium text-base-700 mb-2">No Snoozed Emails</h3>
      <p class="text-sm text-base-500">Emails you snooze will appear here until they resurface.</p>
    </div>

    <!-- Snoozed emails list -->
    <div v-else class="w-full h-full relative overflow-y-auto slider-container px-2 custom-scrollbar py-2">
      <ul class="flex flex-col gap-2 items-center">
        <li
          v-for="snoozedEmail in snoozedEmails"
          :key="snoozedEmail.snooze_log.id"
          class="relative group w-full drop-shadow-md cursor-pointer transition-all duration-200"
        >
          <SnoozedEmailCard :snoozed-email="snoozedEmail" @view-email="viewEmail" @unsnooze="unsnoozeEmail" />
        </li>
      </ul>
    </div>

    <!-- Email detail view -->
    <div>
      <EmailDetailView
        v-if="selectedEmail && selectedThreadId"
        :key="selectedThreadId"
        :emails="[selectedEmail]"
        :thread-id="selectedThreadId"
        :access_token="currentUserStore.currentUser?.access_token"
        @close="closeEmail"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";
import { Email } from "../../types";
import { SnoozedEmailWithDetails } from "../../models/cm-model";
import { getSnoozedEmailsWithDetails } from "../../services/cm-service";
import { useCurrentUserStore } from "../../stores/currentUser";
import { useCurrentEmailCategoryStore } from "../../stores/currentSession";
import { toast } from "vue-sonner";
import { unsnoozeEmail as unsnoozeEmailCommand } from "../../commands/snooze";
import EmailDetailView from "./EmailPageView.vue";
import SnoozedEmailCard from "./SnoozedEmailCard.vue";

const currentUserStore = useCurrentUserStore();
const currentEmailCategoryStore = useCurrentEmailCategoryStore();

const snoozedEmails = ref<SnoozedEmailWithDetails[]>([]);
const isLoading = ref(false);
const selectedEmail = ref<Email | null>(null);
const selectedThreadId = ref<string | null>(null);

const loadSnoozedEmails = async () => {
  if (isLoading.value) return;

  console.log("SnoozedEmailsSection: Loading snoozed emails...");
  isLoading.value = true;
  try {
    const emails = await getSnoozedEmailsWithDetails();
    console.log("SnoozedEmailsSection: Received emails:", emails);
    snoozedEmails.value = emails;
    console.log("SnoozedEmailsSection: Loaded snoozed emails with details:", emails.length);
  } catch (error) {
    console.error("SnoozedEmailsSection: Failed to load snoozed emails:", error);
    toast.error("Failed to load snoozed emails");
  } finally {
    isLoading.value = false;
  }
};

const refreshSnoozedEmails = async () => {
  await loadSnoozedEmails();
};

const viewEmail = (email: Email) => {
  selectedEmail.value = email;
  selectedThreadId.value = email.thread_id || null;
};

const closeEmail = () => {
  selectedEmail.value = null;
  selectedThreadId.value = null;
};

const unsnoozeEmail = async (snoozedEmailId: string) => {
  try {
    // Remove from local state immediately for better UX
    snoozedEmails.value = snoozedEmails.value.filter((email) => email.snooze_log.id !== snoozedEmailId);

    // Call the backend to actually delete the snooze log
    await unsnoozeEmailCommand(snoozedEmailId);

    console.log("Email unsnoozed successfully:", snoozedEmailId);
    toast.success("Email unsnoozed successfully");
  } catch (error) {
    console.error("Failed to unsnooze email:", error);
    toast.error("Failed to unsnooze email");
    // Reload the list to restore the correct state
    await loadSnoozedEmails();
  }
};

// Watch for when this component becomes active and refresh
watch(
  () => currentEmailCategoryStore.currentEmailCategory?.category_id,
  async (newCategoryId) => {
    if (newCategoryId === "snoozed_emails") {
      console.log("Snoozed emails category selected, refreshing...");
      await loadSnoozedEmails();
    }
  },
  { immediate: true }
);

onMounted(async () => {
  await loadSnoozedEmails();
});
</script>

<style scoped>
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}
</style>
