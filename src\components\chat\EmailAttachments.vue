<template>
  <div v-if="attachments.length > 0" class="mt-4 mb-6">
    <div class="bg-primary-50 border border-primary-200 rounded-lg p-4">
      <h3 class="text-sm font-semibold text-secondary-700 mb-3 flex items-center gap-2">
        <PaperClipIcon class="w-4 h-4" />
        Attachments ({{ attachments.length }})
      </h3>
      
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
        <div
          v-for="(attachment, index) in attachments"
          :key="index"
          class="flex items-center gap-3 p-3 bg-primary-100 rounded-lg border border-primary-200 
                 hover:bg-primary-200 transition-colors duration-200 cursor-pointer group"
          @click="downloadAttachment(attachment)"
        >
          <!-- File Icon -->
          <div class="flex-shrink-0 w-10 h-10 rounded-lg bg-secondary-200 flex items-center justify-center">
            <i :class="getFileIconClass(attachment)" class="text-secondary-600 text-lg"></i>
          </div>
          
          <!-- File Info -->
          <div class="flex-1 min-w-0">
            <div class="text-sm font-medium text-secondary-800 truncate" :title="attachment">
              {{ getFileName(attachment) }}
            </div>
            <div class="text-xs text-secondary-500">
              {{ getFileExtension(attachment).toUpperCase() }} file
            </div>
          </div>
          
          <!-- Download Icon -->
          <div class="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <ArrowDownTrayIcon class="w-4 h-4 text-secondary-600" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { PaperClipIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/outline'

interface Props {
  attachments?: string | string[]
}

const props = withDefaults(defineProps<Props>(), {
  attachments: () => []
})

const attachments = computed(() => {
  if (!props.attachments) return []
  
  if (typeof props.attachments === 'string') {
    try {
      const parsed = JSON.parse(props.attachments)
      return Array.isArray(parsed) ? parsed.filter(file => file.trim() !== '') : []
    } catch {
      return props.attachments.trim() !== '' ? [props.attachments] : []
    }
  }
  
  return Array.isArray(props.attachments) ? props.attachments.filter(file => file.trim() !== '') : []
})

const getFileName = (filePath: string): string => {
  return filePath.split('/').pop() || filePath
}

const getFileExtension = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''
  return extension
}

const getFileIconClass = (fileName: string): string => {
  const extension = getFileExtension(fileName)
  
  const iconMap: Record<string, string> = {
    // Documents
    pdf: 'fa-file-pdf',
    doc: 'fa-file-word',
    docx: 'fa-file-word',
    txt: 'fa-file-text',
    rtf: 'fa-file-text',
    
    // Spreadsheets
    xls: 'fa-file-excel',
    xlsx: 'fa-file-excel',
    csv: 'fa-file-csv',
    
    // Presentations
    ppt: 'fa-file-powerpoint',
    pptx: 'fa-file-powerpoint',
    
    // Images
    jpg: 'fa-file-image',
    jpeg: 'fa-file-image',
    png: 'fa-file-image',
    gif: 'fa-file-image',
    bmp: 'fa-file-image',
    svg: 'fa-file-image',
    webp: 'fa-file-image',
    
    // Videos
    mp4: 'fa-file-video',
    avi: 'fa-file-video',
    mov: 'fa-file-video',
    wmv: 'fa-file-video',
    flv: 'fa-file-video',
    webm: 'fa-file-video',
    
    // Audio
    mp3: 'fa-file-audio',
    wav: 'fa-file-audio',
    flac: 'fa-file-audio',
    aac: 'fa-file-audio',
    ogg: 'fa-file-audio',
    
    // Archives
    zip: 'fa-file-zipper',
    rar: 'fa-file-zipper',
    '7z': 'fa-file-zipper',
    tar: 'fa-file-zipper',
    gz: 'fa-file-zipper',
    
    // Code
    js: 'fa-file-code',
    ts: 'fa-file-code',
    html: 'fa-file-code',
    css: 'fa-file-code',
    php: 'fa-file-code',
    py: 'fa-file-code',
    java: 'fa-file-code',
    cpp: 'fa-file-code',
    c: 'fa-file-code',
    
    // Default
    default: 'fa-file'
  }
  
  return `fa-regular ${iconMap[extension] || iconMap.default}`
}

const downloadAttachment = (attachment: string) => {
  // Emit event to parent component to handle download
  console.log('Download attachment:', attachment)
  // This would typically trigger a download or open the attachment
}
</script>
