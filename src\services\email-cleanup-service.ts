import { invoke } from "@tauri-apps/api/core";

/**
 * Service for cleaning up old emails from today's categories
 */
export class EmailCleanupService {
  /**
   * Removes emails older than 5 days from today's categories by setting their storage_location to null.
   * Snoozed emails are preserved regardless of their age.
   * 
   * @returns Promise<number> - The number of emails that were cleaned up
   */
  static async cleanupOldEmailsFromTodaysCategories(): Promise<number> {
    try {
      const result = await invoke<number>("cleanup_old_emails_from_today_categories");
      console.log(`🧹 Successfully cleaned up ${result} old emails from today's categories`);
      return result;
    } catch (error) {
      console.error("❌ Failed to cleanup old emails:", error);
      throw error;
    }
  }
}

/**
 * Convenience function to cleanup old emails from today's categories
 */
export async function cleanupOldEmailsFromTodaysCategories(): Promise<number> {
  return EmailCleanupService.cleanupOldEmailsFromTodaysCategories();
}
