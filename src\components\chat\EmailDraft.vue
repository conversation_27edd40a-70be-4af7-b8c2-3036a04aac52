<template>
  <div v-if="draft" class="mx-4 mb-6 bg-accent-100 border border-accent-300 rounded-xl p-6 shadow-sm">
    <!-- Draft Header -->
    <div class="flex items-center gap-3 mb-4">
      <div class="w-10 h-10 rounded-full bg-accent-400 flex items-center justify-center">
        <PaperClipIcon class="w-5 h-5 text-accent-100" />
      </div>
      <div>
        <h3 class="text-lg font-semibold text-accent-800">Draft Email</h3>
        <p class="text-sm text-accent-600">Ready to send</p>
      </div>
    </div>

    <!-- Draft Content -->
    <div class="bg-primary-100 rounded-lg p-4 mb-4 border border-accent-200" v-html="draft.body"></div>

    <!-- Draft Actions -->
    <div class="flex items-center justify-center gap-4">
      <button
        @click="$emit('sendDraft', draft)"
        class="flex items-center gap-2 bg-accent-500 hover:bg-accent-600 text-primary-100 px-6 py-2 rounded-lg transition-colors duration-200 font-medium shadow-sm"
      >
        <i class="pi pi-send text-sm"></i>
        Send Draft
      </button>

      <button
        @click="$emit('editDraft', draft)"
        class="flex items-center gap-2 bg-primary-200 hover:bg-primary-300 text-secondary-700 px-4 py-2 rounded-lg transition-colors duration-200 font-medium border border-primary-300"
      >
        <i class="pi pi-pencil text-sm"></i>
        Edit
      </button>

      <button
        @click="$emit('deleteDraft', draft)"
        class="flex items-center gap-2 bg-red-100 hover:bg-red-200 text-red-600 px-4 py-2 rounded-lg transition-colors duration-200 font-medium border border-red-300"
      >
        <TrashIcon class="w-4 h-4" />
        Delete
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PaperClipIcon, TrashIcon } from "@heroicons/vue/24/outline";

import type { Draft } from "../../models/task-model";

interface Props {
  draft?: Draft;
}

defineProps<Props>();

defineEmits<{
  sendDraft: [draft: Draft];
  editDraft: [draft: Draft];
  deleteDraft: [draft: Draft];
}>();
</script>
